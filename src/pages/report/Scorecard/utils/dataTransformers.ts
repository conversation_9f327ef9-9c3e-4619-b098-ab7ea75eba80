import {
  FinancialMetric,
  OccupancyMetric,
  PerformanceRow,
  PropertyInfo,
  RentalMetric,
  ReputationMetric,
  SummaryText,
} from '@/slice/scoreCardSlice';
import {
  FilterOptionsResponse,
  GoogleReviewsResponse,
  JTurnerResponse,
  PropertyData,
  ScoreCardData,
  ScoreCardFinancialResponse,
  ScoreCardOccupancyResponse,
  ScoreCardOperationsResponse,
  ScoreCardRentResponse,
  ScoreCardResponse,
} from '@/types/scorecardTypes';
import {
  getAgedVacantUnitsColor,
  getAvgDaysVacantColor,
  getAvgUnitTurnTimeColor,
  getBadDebtWOColor,
  // getCapitalColor,
  getCapitalExecutionColor,
  getCollectionRecoveryRatioColor,
  getCollectionsMTDColor,
  getControllableNOIColor,
  getControllableOpexColor,
  getIncomePerformanceToBudgetColor,
  getMetricColor,
  getNOIColor,
  getOccupancyPercentageColor,
  getOccupancyTrendColor,
  getOccupancyTrendT30ChangeColor,
  getPerformanceToSubmarketColor,
  getRentalYoYChangeColor,
  getRepeatServiceTicketsColor,
  getShowsT30Color,
  getTicketsOver72HrsColor,
  getTotalOpexColor,
  getTrendPercentGainLossColor,
  getVarianceToOccupancyColor,
  getYTDRenewalConversionColor,
} from './colorUtils';
import {
  formatCurrency,
  formatCurrencyForTurnCost,
  formatFinancialDateRange,
  formatNumber,
  formatNumberToZero,
  formatPercentage,
  formatPeriod,
} from './formatUtils';

export const transformOccupancyMetrics = (
  scorecardData: ScoreCardResponse | null,
  submarketOccupancy: number | undefined,
  propertyUnits?: number,
): OccupancyMetric[] => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  // Performance to Submarket = occupancy_trend - submarket_occupancy
  const performanceToSubmarket =
    submarketOccupancy !== undefined && data.Occupancy_Trend !== undefined
      ? data.Occupancy_Trend - submarketOccupancy
      : null;

  const varianceToOccupancy =
    data.Occupancy_Non_Rev !== undefined && data.Occupancy_Trend !== undefined
      ? data.Occupancy_Trend - data.Occupancy_Non_Rev
      : null;

  // Trend % Gain/Loss = (gain_loss / units) × 100
  const trendPercentageGainLoss =
    data.gain_loss !== undefined &&
    propertyUnits !== undefined &&
    propertyUnits > 0
      ? (data.gain_loss / propertyUnits) * 100
      : null;

  return [
    {
      label: 'OCCUPANCY NON REV',
      value: formatPercentage(data.Occupancy_Non_Rev),
      isPositive:
        data.Occupancy_Non_Rev !== undefined && data.Occupancy_Non_Rev > 95,
      color:
        data.Occupancy_Non_Rev !== undefined
          ? getOccupancyPercentageColor(data.Occupancy_Non_Rev)
          : undefined,
    },
    {
      label: 'SUBMARKET OCCUPANCY',
      value: formatPercentage(submarketOccupancy),
      isPositive: submarketOccupancy !== undefined && submarketOccupancy > 90,
      color:
        submarketOccupancy !== undefined
          ? getOccupancyPercentageColor(submarketOccupancy)
          : undefined,
    },
    {
      label: 'VARIANCE TO SUBMARKET',
      value: formatPercentage(performanceToSubmarket),
      isPositive: performanceToSubmarket !== null && performanceToSubmarket > 0,
      color:
        performanceToSubmarket !== null
          ? getPerformanceToSubmarketColor(performanceToSubmarket)
          : undefined,
    },
    {
      label: 'OCCUPANCY TREND',
      value: formatPercentage(data.Occupancy_Trend),
      isPositive:
        data.Occupancy_Trend !== undefined && data.Occupancy_Trend > 95,
      color:
        data.Occupancy_Trend !== undefined
          ? getOccupancyTrendColor(data.Occupancy_Trend)
          : undefined,
    },
    {
      label: 'VARIANCE TO OCCUPANCY',
      value: formatPercentage(varianceToOccupancy),
      isPositive: varianceToOccupancy !== null && varianceToOccupancy > 0,
      color:
        varianceToOccupancy !== null
          ? getVarianceToOccupancyColor(varianceToOccupancy)
          : undefined,
    },
    {
      label: 'TREND GAIN/LOSS',
      value: formatPercentage(trendPercentageGainLoss),
      isPositive:
        trendPercentageGainLoss !== null && trendPercentageGainLoss > 0,
      color:
        trendPercentageGainLoss !== null
          ? getTrendPercentGainLossColor(trendPercentageGainLoss)
          : undefined,
    },
    {
      label: 'GAIN / LOSS',
      value: data.gain_loss !== undefined ? data.gain_loss.toString() : '0',
      isPositive: data.gain_loss !== undefined && data.gain_loss > 0,
      color:
        data.gain_loss !== undefined
          ? getMetricColor(data.gain_loss, true)
          : undefined,
    },
    {
      label: 'OCCUPANCY TREND T30 VARIANCE',
      value: formatPercentage(
        data.Occupancy_Trendt30 !== undefined &&
          data.Occupancy_Trend !== undefined
          ? data.Occupancy_Trend - data.Occupancy_Trendt30
          : null,
      ),
      isPositive: false,
      color:
        data.Occupancy_Trendt30 !== undefined &&
        data.Occupancy_Trend !== undefined &&
        submarketOccupancy !== undefined
          ? getOccupancyTrendT30ChangeColor(
              data.Occupancy_Trendt30 - data.Occupancy_Trend,
              data.Occupancy_Trend,
              submarketOccupancy,
            )
          : 'red',
    },
    {
      label: 'UNITS AVAILABLE',
      value: formatNumber(data.units_available),
      isPositive: true,
    },
    {
      label: 'VACANT UNITS',
      value: formatNumber(data.vcnt_units),
      isPositive: true,
    },
    {
      label: 'AGED VACANT UNITS',
      value: formatNumber(data.aged_vacant_units),
      isPositive: true,
      color:
        data.aged_vacant_units !== undefined && propertyUnits !== undefined
          ? getAgedVacantUnitsColor(data.aged_vacant_units, propertyUnits)
          : undefined,
    },
    {
      label: 'AVG AGED DAYS VACANT',
      value: formatNumberToZero(data.avg_vcnt_days),
      isPositive: data.avg_vcnt_days !== undefined && data.avg_vcnt_days < 30,
      color:
        data.avg_vcnt_days !== undefined
          ? getAvgDaysVacantColor(data.avg_vcnt_days)
          : undefined,
    },
    {
      label: 'SHOWS T30',
      value: formatNumber(data.t30_show),
      isPositive: true,
      color:
        data.t30_show !== undefined
          ? getShowsT30Color(data.t30_show, propertyUnits)
          : undefined,
    },
  ];
};

export const transformRentalMetrics = (
  scorecardData: ScoreCardResponse | null,
): RentalMetric[] => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  // YOY NEW NET NET IN PLACE RENT = ((new_in_place_rent_current_year - new_in_place_rent_prev_year) / new_in_place_rent_prev_year) × 100
  const newRentYoY =
    data.New_In_Place_rent !== undefined &&
    data.Prev_Yr_New_In_Place_rent !== undefined &&
    data.Prev_Yr_New_In_Place_rent !== 0
      ? ((data.New_In_Place_rent - data.Prev_Yr_New_In_Place_rent) /
          data.Prev_Yr_New_In_Place_rent) *
        100
      : null;

  // YOY Renewal In Place = ((renewal_in_place_rent_current_year - renewal_in_place_rent_prev_year) / renewal_in_place_rent_prev_year) × 100
  const renewalRentYoY =
    data.Renewal_In_Place_rent !== undefined &&
    data.Prev_Yr_Renewal_In_Place_rent !== undefined &&
    data.Prev_Yr_Renewal_In_Place_rent !== 0
      ? ((data.Renewal_In_Place_rent - data.Prev_Yr_Renewal_In_Place_rent) /
          data.Prev_Yr_Renewal_In_Place_rent) *
        100
      : null;

  // YOY NET IN PLACE RENT = ((in_place_rent_current_year - in_place_rent_prev_year) / in_place_rent_prev_year) × 100
  const inPlaceRentYoY =
    data.In_Place_rent !== undefined &&
    data.Prev_Yr_In_Place_rent !== undefined &&
    data.Prev_Yr_In_Place_rent !== 0
      ? ((data.In_Place_rent - data.Prev_Yr_In_Place_rent) /
          data.Prev_Yr_In_Place_rent) *
        100
      : null;

  return [
    {
      label: 'NEW NET IN PLACE RENT',
      value:
        data.New_In_Place_rent !== undefined &&
        data['New_In_Place_rent/sqft'] !== undefined
          ? `${formatCurrencyForTurnCost(data.New_In_Place_rent)} / ${formatCurrency(data['New_In_Place_rent/sqft'])}`
          : '0',
      isPositive: true,
    },
    {
      label: 'NEW NET IN PLACE RENT YoY CHANGE',
      value: formatPercentage(newRentYoY),
      isPositive: newRentYoY !== null && newRentYoY > 0,
      color:
        newRentYoY !== null ? getRentalYoYChangeColor(newRentYoY) : undefined,
    },
    {
      label: 'RENEWAL NET IN PLACE RENT',
      value:
        data.Renewal_In_Place_rent !== undefined &&
        data['Renewal_In_Place_rent/sqft'] !== undefined
          ? `${formatCurrencyForTurnCost(data.Renewal_In_Place_rent)} / ${formatCurrency(data['Renewal_In_Place_rent/sqft'])}`
          : '0',
      isPositive: true,
    },
    {
      label: 'RENEWAL NET IN PLACE RENT YoY CHANGE',
      value: formatPercentage(renewalRentYoY),
      isPositive: renewalRentYoY !== null && renewalRentYoY > 0,
      color:
        renewalRentYoY !== null
          ? getRentalYoYChangeColor(renewalRentYoY)
          : undefined,
    },
    {
      label: 'NET IN PLACE RENT',
      value:
        data.In_Place_rent !== undefined &&
        data['In_Place_rent/sqft'] !== undefined
          ? `${formatCurrencyForTurnCost(data.In_Place_rent)} / ${formatCurrency(data['In_Place_rent/sqft'])}`
          : '0',
      isPositive: true,
    },
    {
      label: 'NET IN PLACE RENT YoY CHANGE',
      value: formatPercentage(inPlaceRentYoY),
      isPositive: inPlaceRentYoY !== null && inPlaceRentYoY > 0,
      color:
        inPlaceRentYoY !== null
          ? getRentalYoYChangeColor(inPlaceRentYoY)
          : undefined,
    },
    {
      label: 'MTM',
      value: formatPercentage(data.MTM),
      isPositive: true,
    },
    {
      label: 'YTD RENEWAL CONVERSION',
      value: formatPercentage(data.YTD_Renewal_Conversion, 0),
      isPositive: (data.YTD_Renewal_Conversion || 0) > 60,
      color:
        data.YTD_Renewal_Conversion !== undefined
          ? getYTDRenewalConversionColor(data.YTD_Renewal_Conversion)
          : undefined,
    },
  ];
};

export const getFinancialDateRange = (
  scorecardData: ScoreCardResponse | null,
): string => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  return formatFinancialDateRange(
    data.Adjusted_Period_Start_Month,
    data.Adjusted_Period_Start_Day,
    data.Adjusted_Period_Start_Year,
    data.Adjusted_Period_End_Month,
    data.Adjusted_Period_End_Day,
    data.Adjusted_Period_End_Year,
  );
};

export const transformFinancialMetrics = (
  scorecardData: ScoreCardResponse | null,
): FinancialMetric[] => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  return [
    {
      label: 'RENTAL INCOME',
      value: formatPercentage(data.Rental_Income),
      isPositive: (data.Rental_Income || 0) > 0,
      color:
        data.Rental_Income !== undefined
          ? getIncomePerformanceToBudgetColor(data.Rental_Income)
          : undefined,
    },
    {
      label: 'TOTAL INCOME',
      value: formatPercentage(data.Income),
      isPositive: (data.Income || 0) > 0,
      color:
        data.Income !== undefined
          ? getIncomePerformanceToBudgetColor(data.Income)
          : undefined,
    },
    {
      label: 'CONTROLLABLE OP EX',
      value: formatPercentage(data.Controllable_Opex),
      isPositive: (data.Controllable_Opex || 0) > 0,
      color:
        data.Controllable_Opex !== undefined
          ? getControllableOpexColor(data.Controllable_Opex)
          : undefined,
    },
    {
      label: 'TOTAL OP EX',
      value: formatPercentage(data.total_OPex),
      isPositive: (data.total_OPex || 0) > 0,
      color:
        data.total_OPex !== undefined
          ? getTotalOpexColor(data.total_OPex)
          : undefined,
    },
    {
      label: 'NOI',
      value: formatPercentage(data.NOI),
      isPositive: (data.NOI || 0) > 0,
      color: data.NOI !== undefined ? getNOIColor(data.NOI) : undefined,
    },
    {
      label: 'CONTROLLABLE NOI',
      value: formatPercentage(data.Controll_NOI),
      isPositive: (data.Controll_NOI || 0) > 0,
      color:
        data.Controll_NOI !== undefined
          ? getControllableNOIColor(data.Controll_NOI)
          : undefined,
    },
    // {
    //   label: 'CAPITAL',
    //   value: formatPercentage(data.Capital),
    //   isPositive: (data.Capital || 0) > 0,
    //   color:
    //     data.Capital !== undefined ? getCapitalColor(data.Capital) : undefined,
    // },
  ];
};

export const transformPerformanceRows = (
  scorecardData: ScoreCardResponse | null,
  propertyUnits?: number,
): PerformanceRow[] => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  const getScore = (
    category: string,
    actual: number,
  ): 'green' | 'yellow' | 'red' => {
    switch (category) {
      case 'Collections % MTD':
        return getCollectionsMTDColor(actual) || 'green';
      case 'Bad Debt W/O':
        return getBadDebtWOColor(actual) || 'green';
      case 'Collection Recovery':
        return getCollectionRecoveryRatioColor(actual) || 'green';
      case 'Turn Time':
        return getAvgUnitTurnTimeColor(actual) || 'green';
      case 'Repeat Tickets':
        return getRepeatServiceTicketsColor(actual) || 'green';
      case 'Tickets >72':
        return getTicketsOver72HrsColor(actual) || 'green';
      case 'Capital Execution':
        return getCapitalExecutionColor(actual) || 'green';
      default:
        return 'green';
    }
  };

  const endDate =
    data.Adjusted_Period_End_Year &&
    data.Adjusted_Period_End_Month &&
    data.Adjusted_Period_End_Day
      ? new Date(
          data.Adjusted_Period_End_Year,
          data.Adjusted_Period_End_Month - 1,
          data.Adjusted_Period_End_Day,
        )
      : new Date();
  const periodEnd = endDate.toLocaleDateString('en-US');

  return [
    {
      category: 'Collections % MTD',
      period: `As of ${periodEnd}`,
      target: '98.0%',
      actual: formatPercentage(data.collection_MTD),
      score: getScore('Collections % MTD', data.collection_MTD || 0),
    },
    {
      category: 'Bad Debt W/O (Net) as % of GRI (Market Rents) YTD',
      period: formatPeriod('2024-12-22', '2025-04-21'),
      target: '-1.0%',
      actual: formatPercentage(data['bad_debt_w/o_%_GRI']),
      score: getScore(
        'Bad Debt W/O',
        Math.abs(data['bad_debt_w/o_%_GRI'] || 0),
      ),
    },
    {
      category: 'Collection Recovery Ratio (In House & Agency)',
      period: formatPeriod('2024-12-22', '2025-04-21'),
      target: '18.0%',
      actual: formatPercentage(data.Collect_recovery_ratio),
      score: getScore('Collection Recovery', data.Collect_recovery_ratio || 0),
    },
    {
      category: 'Average Unit Turn Time T90',
      period: 'T90',
      target: '10 Days',
      actual:
        data.avg_turn_time !== undefined ? data.avg_turn_time.toString() : '0',
      score: getScore('Turn Time', data.avg_turn_time || 0),
    },
    {
      category: 'Units With Repeat Service Tickets Within 30 Days',
      period: 'T30',
      target: propertyUnits
        ? `< 2% of Total Units (${(propertyUnits * 0.02).toFixed(1)})`
        : '< 2% of Total Units',
      actual:
        data.repeat_tickets !== undefined
          ? data.repeat_tickets.toString()
          : '0',
      score: getScore('Repeat Tickets', data.repeat_tickets || 0),
    },
    {
      category: '% Tickets >72 Hours To Close T30',
      period: 'T30',
      target: '< 5% of Total Tickets',
      actual: formatPercentage(data.outstanding_tickets),
      score: getScore('Tickets >72', data.outstanding_tickets || 0),
    },
    {
      category: 'Capital Execution',
      period: formatPeriod('2024-12-22', '2025-04-21'),
      target: '85% budget execution',
      actual: formatPercentage(data.capital_execution),
      score: getScore('Capital Execution', data.capital_execution || 0),
    },
  ];
};

export const transformPropertyInfo = (
  filterOptions: FilterOptionsResponse | null,
  propertyCode: string,
  propertyStrategy: string,
  sameStore: string,
  imageUrl?: string,
): PropertyInfo => {
  const properties = filterOptions?.data?.properties || [];
  const property =
    properties.find((p: PropertyData) => p.BU === propertyCode) ||
    ({} as Partial<PropertyData>);

  return {
    propertyCode: property.BU || '0',
    propertyName: property.Property || '0',
    address: property.Address || '0',
    city: property.City || '0',
    state: property.State || '0',
    zipCode: property.ZipCode || '0',
    submarket: property.census_submarket || '0',
    units: parseInt(property.UnitCount || '0') || 0,
    begOfOps: property.BeginningOfOperations || '0',
    assetType: property.AssetType || '0',
    yearBuilt: property.Age ? parseInt(property.Age || '0') : 0,
    residentialSqft: 0,
    avgResUnitSize: 0,
    retailSqft: '0',
    retailSpaces: 0,
    assetClass: property.Class || '0',
    region: property.Region || '0',
    svp: '0',
    rvp: property.RVP || '0',
    vp: property.VP || '0',
    rpm: property.RPM || '0',
    renewableUnits: 0,
    nonRevenueUnits: 0,
    downUnits: 0,
    propertyStrategy: propertyStrategy || '0',
    sameStore: sameStore || '0',
    imageUrl: imageUrl,
  };
};

export const updatePropertyInfoWithScorecard = (
  propertyInfo: PropertyInfo,
  scorecardData: ScoreCardResponse | null,
): PropertyInfo => {
  const data = scorecardData?.data || ({} as Partial<ScoreCardData>);

  return {
    ...propertyInfo,
    residentialSqft: data.Residential_sqft || propertyInfo.residentialSqft,
    avgResUnitSize: data.AVG_Residential_sqft || propertyInfo.avgResUnitSize,
    retailSqft: data.Retail_sqft ? formatNumber(data.Retail_sqft) : '0',
    retailSpaces: data.Retail_Spaces || 0,
    nonRevenueUnits: data.Non_Revenue || 0,
    downUnits: data.Down || 0,
  };
};

export const transformReputationMetrics = (
  jTurnerData: JTurnerResponse | null,
  googleData: GoogleReviewsResponse | null,
): ReputationMetric[] => {
  const jTurnerScore = jTurnerData?.data?.[0]?.ora_score;
  const googleRating = googleData?.data?.[0]?.rating;

  return [
    {
      platform: 'J Turner',
      rating: jTurnerScore ? parseFloat(jTurnerScore) : 0,
      maxRating: 100,
    },
    {
      platform: 'Google',
      rating: googleRating ? parseFloat(googleRating) : 0,
      maxRating: 5,
    },
  ];
};

export const generateSummaryText = (
  propertyName: string,
  occupancyTrend: number,
  submarketOccupancy: number,
  gainLoss: number,
  showsT30: number,
  occupancyTrendT30Var: number,
  units?: number,
): SummaryText[] => {
  // Performance to Submarket = occupancy_trend - submarket_occupancy
  const performanceToSubmarket = occupancyTrend - submarketOccupancy;
  const exceedingOrTrailing =
    performanceToSubmarket > 0 ? 'exceeding' : 'trailing';
  const gainOrLoss = gainLoss > 0 ? 'gain' : 'loss';

  // Trend % Gain/Loss = (gain_loss / units) × 100
  const trendPercentGainLoss =
    units && units > 0 ? (gainLoss / units) * 100 : 0;
  const increaseOrDecrease = trendPercentGainLoss > 0 ? 'increase' : 'decrease';
  const increasedOrDeclined =
    occupancyTrendT30Var > 0 ? 'increased' : 'declined';

  const content =
    propertyName !== '0' && !isNaN(occupancyTrend) && !isNaN(submarketOccupancy)
      ? `${propertyName} is ${exceedingOrTrailing} submarket occupancy by ${Math.abs(performanceToSubmarket).toFixed(1)}%. ` +
        `There was a net ${gainOrLoss} of ${Math.abs(gainLoss)} units, representing a ${Math.abs(trendPercentGainLoss).toFixed(1)}% ${increaseOrDecrease} in occupancy trend. ` +
        `There were ${showsT30} shows in the last 30 days. ` +
        `Occupancy Trend ${increasedOrDeclined} by ${Math.abs(occupancyTrendT30Var).toFixed(1)}% in the last 30 days.`
      : '0';

  return [
    {
      title: 'Summary',
      content,
    },
  ];
};

/**
 * Merges the 4 separate API responses into a single ScoreCardData structure
 * for backward compatibility with existing transformation functions
 */
export const mergeScoreCardResponses = (
  occupancyResponse: ScoreCardOccupancyResponse | null,
  rentResponse: ScoreCardRentResponse | null,
  financialResponse: ScoreCardFinancialResponse | null,
  operationsResponse: ScoreCardOperationsResponse | null,
): ScoreCardData => {
  const occupancyData = occupancyResponse?.data;
  const rentData = rentResponse?.data;
  const financialData = financialResponse?.data;
  const operationsData = operationsResponse?.data;

  return {
    // From Occupancy API
    vcnt_units: occupancyData?.vcnt_units || 0,
    avg_vcnt_days: occupancyData?.avg_vcnt_days || 0,
    aged_vacant_units: occupancyData?.aged_vacant_units || 0,
    Occupancy_Non_Rev: occupancyData?.Occupancy_Non_Rev || 0,
    Occupancy_Trend: occupancyData?.Occupancy_Trend || 0,
    gain_loss: occupancyData?.gain_loss || 0,
    units_available: occupancyData?.units_available || 0,
    t30_show: occupancyData?.t30_show || 0,
    Occupancy_Trendt30: occupancyData?.Occupancy_Trendt30 || 0,

    // From Rent API
    In_Place_rent: rentData?.In_Place_rent || 0,
    'In_Place_rent/sqft': rentData?.['In_Place_rent/sqft'] || 0,
    Prev_Yr_In_Place_rent: rentData?.Prev_Yr_In_Place_rent || 0,
    New_In_Place_rent: rentData?.New_In_Place_rent || 0,
    'New_In_Place_rent/sqft': rentData?.['New_In_Place_rent/sqft'] || 0,
    Prev_Yr_New_In_Place_rent: rentData?.Prev_Yr_New_In_Place_rent || 0,
    Renewal_In_Place_rent: rentData?.Renewal_In_Place_rent || 0,
    'Renewal_In_Place_rent/sqft': rentData?.['Renewal_In_Place_rent/sqft'] || 0,
    Prev_Yr_Renewal_In_Place_rent: rentData?.Prev_Yr_Renewal_In_Place_rent || 0,
    MTM: rentData?.MTM || 0,
    YTD_Renewal_Conversion: rentData?.YTD_Renewal_Conversion || 0,

    // From Financial API
    Income: financialData?.Income || 0,
    Controllable_Opex: financialData?.Controllable_Opex || 0,
    total_OPex: financialData?.total_OPex || 0,
    NOI: financialData?.NOI || 0,
    Controll_NOI: financialData?.Controll_NOI || 0,
    Capital: financialData?.Capital || 0,
    COST_Per_Turn: financialData?.COST_Per_Turn || 0,

    // From Operations API
    outstanding_tickets: operationsData?.outstanding_tickets || 0,
    collection_MTD: operationsData?.collection_MTD || 0,
    capital_execution: operationsData?.capital_execution || 0,
    avg_turn_time: operationsData?.avg_turn_time || 0,
    repeat_tickets: operationsData?.repeat_tickets || 0,
    Residential_sqft: operationsData?.Residential_sqft || 0,
    AVG_Residential_sqft: operationsData?.AVG_Residential_sqft || 0,
    Retail_sqft: operationsData?.Retail_sqft || 0,
    Retail_Spaces: operationsData?.Retail_Spaces || 0,
    Affordable: operationsData?.Affordable || 0,
    Non_Revenue: operationsData?.Non_Revenue || 0,
    Down: operationsData?.Down || 0,
    Adjusted_Period_Start_Month:
      operationsData?.Adjusted_Period_Start_Month || 0,
    Adjusted_Period_Start_Day: operationsData?.Adjusted_Period_Start_Day || 0,
    Adjusted_Period_Start_Year: operationsData?.Adjusted_Period_Start_Year || 0,
    Adjusted_Period_End_Month: operationsData?.Adjusted_Period_End_Month || 0,
    Adjusted_Period_End_Day: operationsData?.Adjusted_Period_End_Day || 0,
    Adjusted_Period_End_Year: operationsData?.Adjusted_Period_End_Year || 0,
    Collect_recovery_ratio: operationsData?.Collect_recovery_ratio || 0,
    'bad_debt_w/o_%_GRI': operationsData?.['bad_debt_w/o_%_GRI'] || 0,

    // Fields that may not be in the new APIs - set to defaults
    Rental_Income: 0,
  };
};
